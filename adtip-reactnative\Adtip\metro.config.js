const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');
const {withNativeWind} = require('nativewind/metro');

const config = mergeConfig(getDefaultConfig(__dirname), {
  transformer: {
    // Enable Hermes for better performance and Reanimated compatibility
    hermesCommand: 'hermes',
    // Ensure proper transformation of worklets
    unstable_allowRequireContext: true,
  },
  resolver: {
    // Ensure proper resolution of Reanimated modules
    alias: {
      // Add any necessary aliases here if needed
    },
  },
});

module.exports = withNativeWind(config, {input: './src/global.css'});
