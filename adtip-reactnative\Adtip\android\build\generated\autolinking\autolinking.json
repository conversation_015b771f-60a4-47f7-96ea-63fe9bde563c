{"root": "F:\\A1\\adtip-reactnative\\Adtip", "reactNativePath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native", "reactNativeVersion": "0.79", "dependencies": {"@d11/react-native-fast-image": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@d11\\react-native-fast-image", "name": "@d11/react-native-fast-image", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@d11\\react-native-fast-image\\RNFastImage.podspec", "version": "8.9.2", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@d11\\react-native-fast-image\\android", "packageImportPath": "import com.dylanvann.fastimage.FastImageViewPackage;", "packageInstance": "new FastImageViewPackage()", "buildTypes": [], "libraryName": "RNFastImageSpec", "componentDescriptors": ["FastImageViewComponentDescriptor"], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@notifee/react-native": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@notifee\\react-native", "name": "@notifee/react-native", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@notifee\\react-native\\RNNotifee.podspec", "version": "9.1.8", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@notifee\\react-native\\android", "packageImportPath": "import io.invertase.notifee.NotifeePackage;", "packageInstance": "new NotifeePackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/@notifee/react-native/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@nozbe/watermelondb": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@nozbe\\watermelondb", "name": "@nozbe/watermelondb", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@nozbe\\watermelondb\\WatermelonDB.podspec", "version": "0.28.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@nozbe\\watermelondb\\native\\android", "packageImportPath": "import com.nozbe.watermelondb.WatermelonDBPackage;", "packageInstance": "new WatermelonDBPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/@nozbe/watermelondb/native/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-async-storage/async-storage": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-async-storage\\async-storage", "name": "@react-native-async-storage/async-storage", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-async-storage\\async-storage\\RNCAsyncStorage.podspec", "version": "2.2.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-async-storage\\async-storage\\android", "packageImportPath": "import com.reactnativecommunity.asyncstorage.AsyncStoragePackage;", "packageInstance": "new AsyncStoragePackage()", "buildTypes": [], "libraryName": "rnasyncstorage", "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-clipboard/clipboard": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-clipboard\\clipboard", "name": "@react-native-clipboard/clipboard", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-clipboard\\clipboard\\RNCClipboard.podspec", "version": "1.16.3", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-clipboard\\clipboard\\android", "packageImportPath": "import com.reactnativecommunity.clipboard.ClipboardPackage;", "packageInstance": "new ClipboardPackage()", "buildTypes": [], "libraryName": "rnclipboard", "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-community/blur": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\blur", "name": "@react-native-community/blur", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\blur\\react-native-blur.podspec", "version": "4.4.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\blur\\android", "packageImportPath": "import com.reactnativecommunity.blurview.BlurViewPackage;", "packageInstance": "new BlurViewPackage()", "buildTypes": [], "libraryName": "rnblurview", "componentDescriptors": ["BlurViewComponentDescriptor", "AndroidBlurViewComponentDescriptor", "VibrancyViewComponentDescriptor"], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-community/datetimepicker": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\datetimepicker", "name": "@react-native-community/datetimepicker", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\datetimepicker\\RNDateTimePicker.podspec", "version": "8.4.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\datetimepicker\\android", "packageImportPath": "import com.reactcommunity.rndatetimepicker.RNDateTimePickerPackage;", "packageInstance": "new RNDateTimePickerPackage()", "buildTypes": [], "libraryName": "RNDateTimePickerCGen", "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-community/masked-view": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\masked-view", "name": "@react-native-community/masked-view", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\masked-view\\RNCMaskedView.podspec", "version": "0.1.11", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\masked-view\\android", "packageImportPath": "import org.reactnative.maskedview.RNCMaskedViewPackage;", "packageInstance": "new RNCMaskedViewPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/masked-view/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-community/netinfo": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\netinfo", "name": "@react-native-community/netinfo", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\netinfo\\react-native-netinfo.podspec", "version": "11.4.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\netinfo\\android", "packageImportPath": "import com.reactnativecommunity.netinfo.NetInfoPackage;", "packageInstance": "new NetInfoPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/netinfo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-firebase/analytics": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\analytics", "name": "@react-native-firebase/analytics", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\analytics\\RNFBAnalytics.podspec", "version": "22.2.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\analytics\\android", "packageImportPath": "import io.invertase.firebase.analytics.ReactNativeFirebaseAnalyticsPackage;", "packageInstance": "new ReactNativeFirebaseAnalyticsPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-firebase/analytics/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-firebase/app": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\app", "name": "@react-native-firebase/app", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\app\\RNFBApp.podspec", "version": "22.2.1", "configurations": [], "scriptPhases": [{"name": "[RNFB] Core Configuration", "path": "./ios_config.sh", "execution_position": "after_compile", "input_files": ["$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)"]}]}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\app\\android", "packageImportPath": "import io.invertase.firebase.app.ReactNativeFirebaseAppPackage;", "packageInstance": "new ReactNativeFirebaseAppPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-firebase/app/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-firebase/auth": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\auth", "name": "@react-native-firebase/auth", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\auth\\RNFBAuth.podspec", "version": "22.2.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\auth\\android", "packageImportPath": "import io.invertase.firebase.auth.ReactNativeFirebaseAuthPackage;", "packageInstance": "new ReactNativeFirebaseAuthPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-firebase/auth/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-firebase/crashlytics": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\crashlytics", "name": "@react-native-firebase/crashlytics", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\crashlytics\\RNFBCrashlytics.podspec", "version": "22.2.1", "configurations": [], "scriptPhases": [{"name": "[RNFB] Crashlytics Configuration", "path": "./ios_config.sh", "execution_position": "after_compile", "input_files": ["${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}", "$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)"]}]}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\crashlytics\\android", "packageImportPath": "import io.invertase.firebase.crashlytics.ReactNativeFirebaseCrashlyticsPackage;", "packageInstance": "new ReactNativeFirebaseCrashlyticsPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-firebase/crashlytics/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-firebase/database": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\database", "name": "@react-native-firebase/database", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\database\\RNFBDatabase.podspec", "version": "22.2.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\database\\android", "packageImportPath": "import io.invertase.firebase.database.ReactNativeFirebaseDatabasePackage;", "packageInstance": "new ReactNativeFirebaseDatabasePackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-firebase/database/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-firebase/firestore": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\firestore", "name": "@react-native-firebase/firestore", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\firestore\\RNFBFirestore.podspec", "version": "22.2.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\firestore\\android", "packageImportPath": "import io.invertase.firebase.firestore.ReactNativeFirebaseFirestorePackage;", "packageInstance": "new ReactNativeFirebaseFirestorePackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-firebase/firestore/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-firebase/messaging": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\messaging", "name": "@react-native-firebase/messaging", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\messaging\\RNFBMessaging.podspec", "version": "22.2.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\messaging\\android", "packageImportPath": "import io.invertase.firebase.messaging.ReactNativeFirebaseMessagingPackage;", "packageInstance": "new ReactNativeFirebaseMessagingPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-firebase/messaging/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-firebase/storage": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\storage", "name": "@react-native-firebase/storage", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\storage\\RNFBStorage.podspec", "version": "22.2.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-firebase\\storage\\android", "packageImportPath": "import io.invertase.firebase.storage.ReactNativeFirebaseStoragePackage;", "packageInstance": "new ReactNativeFirebaseStoragePackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-firebase/storage/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@shopify/react-native-skia": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia", "name": "@shopify/react-native-skia", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\react-native-skia.podspec", "version": "2.0.3", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android", "packageImportPath": "import com.shopify.reactnative.skia.RNSkiaPackage;", "packageInstance": "new RNSkiaPackage()", "buildTypes": [], "libraryName": "rnskia", "componentDescriptors": ["SkiaPictureViewComponentDescriptor"], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@videosdk.live/react-native-incallmanager": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@videosdk.live\\react-native-incallmanager", "name": "@videosdk.live/react-native-incallmanager", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@videosdk.live\\react-native-incallmanager\\ReactNativeIncallManager.podspec", "version": "0.1.3", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@videosdk.live\\react-native-incallmanager\\android", "packageImportPath": "import live.videosdk.rnincallmanager.InCallManagerPackage;", "packageInstance": "new InCallManagerPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/@videosdk.live/react-native-incallmanager/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@videosdk.live/react-native-webrtc": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@videosdk.live\\react-native-webrtc", "name": "@videosdk.live/react-native-webrtc", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@videosdk.live\\react-native-webrtc\\react-native-webrtc.podspec", "version": "0.0.19", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@videosdk.live\\react-native-webrtc\\android", "packageImportPath": "import live.videosdk.rnwebrtc.WebRTCModulePackage;", "packageInstance": "new WebRTCModulePackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/@videosdk.live/react-native-webrtc/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "lottie-react-native": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\lottie-react-native", "name": "lottie-react-native", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\lottie-react-native\\lottie-react-native.podspec", "version": "7.2.2", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\lottie-react-native\\android", "packageImportPath": "import com.airbnb.android.react.lottie.LottiePackage;", "packageInstance": "new LottiePackage()", "buildTypes": [], "libraryName": "lottiereactnative", "componentDescriptors": ["LottieAnimationViewComponentDescriptor"], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-callkeep": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-callkeep", "name": "react-native-callkeep", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-callkeep\\RNCallKeep.podspec", "version": "4.3.16", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-callkeep\\android", "packageImportPath": "import io.wazo.callkeep.RNCallKeepPackage;", "packageInstance": "new RNCallKeepPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-callkeep/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-compressor": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-compressor", "name": "react-native-compressor", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-compressor\\react-native-compressor.podspec", "version": "1.11.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-compressor\\android", "packageImportPath": "import com.reactnativecompressor.CompressorPackage;", "packageInstance": "new CompressorPackage()", "buildTypes": [], "libraryName": "Compressor", "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-create-thumbnail": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-create-thumbnail", "name": "react-native-create-thumbnail", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-create-thumbnail\\react-native-create-thumbnail.podspec", "version": "2.1.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-create-thumbnail\\android", "packageImportPath": "import com.reactlibrary.createthumbnail.CreateThumbnailPackage;", "packageInstance": "new CreateThumbnailPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-create-thumbnail/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-date-picker": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-date-picker", "name": "react-native-date-picker", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-date-picker\\react-native-date-picker.podspec", "version": "5.0.13", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-date-picker\\android", "packageImportPath": "import com.henninghall.date_picker.DatePickerPackage;", "packageInstance": "new DatePickerPackage()", "buildTypes": [], "libraryName": "RNDatePickerSpecs", "componentDescriptors": ["RNDatePickerComponentDescriptor"], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-fs": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-fs", "name": "react-native-fs", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-fs\\RNFS.podspec", "version": "2.20.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-fs\\android", "packageImportPath": "import com.rnfs.RNFSPackage;", "packageInstance": "new RNFSPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-fs/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-geolocation-service": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-geolocation-service", "name": "react-native-geolocation-service", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-geolocation-service\\react-native-geolocation-service.podspec", "version": "5.3.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-geolocation-service\\android", "packageImportPath": "import com.agontuk.RNFusedLocation.RNFusedLocationPackage;", "packageInstance": "new RNFusedLocationPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-geolocation-service/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-gesture-handler": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-gesture-handler", "name": "react-native-gesture-handler", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-gesture-handler\\RNGestureHandler.podspec", "version": "2.26.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-gesture-handler\\android", "packageImportPath": "import com.swmansion.gesturehandler.RNGestureHandlerPackage;", "packageInstance": "new RNGestureHandlerPackage()", "buildTypes": [], "libraryName": "rngesturehandler_codegen", "componentDescriptors": ["RNGestureHandlerButtonComponentDescriptor", "RNGestureHandlerRootViewComponentDescriptor"], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-get-random-values": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-get-random-values", "name": "react-native-get-random-values", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-get-random-values\\react-native-get-random-values.podspec", "version": "1.11.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-get-random-values\\android", "packageImportPath": "import org.linusu.RNGetRandomValuesPackage;", "packageInstance": "new RNGetRandomValuesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-get-random-values/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-google-mobile-ads": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-google-mobile-ads", "name": "react-native-google-mobile-ads", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-google-mobile-ads\\RNGoogleMobileAds.podspec", "version": "15.4.0", "configurations": [], "scriptPhases": [{"name": "[RNGoogleMobileAds] Configuration", "path": "./ios_config.sh", "execution_position": "after_compile", "input_files": ["$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)"]}]}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-google-mobile-ads\\android", "packageImportPath": "import io.invertase.googlemobileads.ReactNativeGoogleMobileAdsPackage;", "packageInstance": "new ReactNativeGoogleMobileAdsPackage()", "buildTypes": [], "libraryName": "RNGoogleMobileAdsSpec", "componentDescriptors": ["RNGoogleMobileAdsBannerViewComponentDescriptor", "RNGoogleMobileAdsMediaViewComponentDescriptor", "RNGoogleMobileAdsNativeViewComponentDescriptor"], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-image-crop-picker": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-crop-picker", "name": "react-native-image-crop-picker", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-crop-picker\\RNImageCropPicker.podspec", "version": "0.50.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-crop-picker\\android", "packageImportPath": "import com.reactnative.ivpusic.imagepicker.PickerPackage;", "packageInstance": "new PickerPackage()", "buildTypes": [], "libraryName": "RNCImageCropPickerSpec", "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-image-picker": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-picker", "name": "react-native-image-picker", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-picker\\react-native-image-picker.podspec", "version": "8.2.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-picker\\android", "packageImportPath": "import com.imagepicker.ImagePickerPackage;", "packageInstance": "new ImagePickerPackage()", "buildTypes": [], "libraryName": "RNImagePickerSpec", "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-linear-gradient": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-linear-gradient", "name": "react-native-linear-gradient", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-linear-gradient\\BVLinearGradient.podspec", "version": "2.8.3", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-linear-gradient\\android", "packageImportPath": "import com.BV.LinearGradient.LinearGradientPackage;", "packageInstance": "new LinearGradientPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-linear-gradient/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-orientation-locker": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-orientation-locker", "name": "react-native-orientation-locker", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-orientation-locker\\react-native-orientation-locker.podspec", "version": "1.7.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-orientation-locker\\android", "packageImportPath": "import org.wonday.orientation.OrientationPackage;", "packageInstance": "new OrientationPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-orientation-locker/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-pager-view": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-pager-view", "name": "react-native-pager-view", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-pager-view\\react-native-pager-view.podspec", "version": "6.8.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-pager-view\\android", "packageImportPath": "import com.reactnativepagerview.PagerViewPackage;", "packageInstance": "new PagerViewPackage()", "buildTypes": [], "libraryName": "pagerview", "componentDescriptors": ["RNCViewPagerComponentDescriptor"], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-permissions": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-permissions", "name": "react-native-permissions", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-permissions\\RNPermissions.podspec", "version": "5.4.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-permissions\\android", "packageImportPath": "import com.zoontek.rnpermissions.RNPermissionsPackage;", "packageInstance": "new RNPermissionsPackage()", "buildTypes": [], "libraryName": "RNPermissionsSpec", "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-razorpay": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-razorpay", "name": "react-native-razorpay", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-razorpay\\react-native-razorpay.podspec", "version": "2.3.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-razorpay\\android", "packageImportPath": "import com.razorpay.rn.RazorpayPackage;", "packageInstance": "new RazorpayPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-razorpay/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-reanimated": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated", "name": "react-native-reanimated", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated\\RNReanimated.podspec", "version": "3.18.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated\\android", "packageImportPath": "import com.swmansion.reanimated.ReanimatedPackage;", "packageInstance": "new ReanimatedPackage()", "buildTypes": [], "libraryName": "rnreanimated", "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-restart": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-restart", "name": "react-native-restart", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-restart\\react-native-restart.podspec", "version": "0.0.27", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-restart\\android", "packageImportPath": "import com.reactnativerestart.RestartPackage;", "packageInstance": "new RestartPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-restart/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-safe-area-context": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-safe-area-context", "name": "react-native-safe-area-context", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-safe-area-context\\react-native-safe-area-context.podspec", "version": "5.4.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-safe-area-context\\android", "packageImportPath": "import com.th3rdwave.safeareacontext.SafeAreaContextPackage;", "packageInstance": "new SafeAreaContextPackage()", "buildTypes": [], "libraryName": "safeareacontext", "componentDescriptors": ["RNCSafeAreaProviderComponentDescriptor", "RNCSafeAreaViewComponentDescriptor"], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-screens": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens", "name": "react-native-screens", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\RNScreens.podspec", "version": "4.11.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\android", "packageImportPath": "import com.swmansion.rnscreens.RNScreensPackage;", "packageInstance": "new RNScreensPackage()", "buildTypes": [], "libraryName": "rnscreens", "componentDescriptors": ["RNSFullWindowOverlayComponentDescriptor", "RNSScreenContainerComponentDescriptor", "RNSScreenNavigationContainerComponentDescriptor", "RNSScreenStackHeaderConfigComponentDescriptor", "RNSScreenStackHeaderSubviewComponentDescriptor", "RNSScreenStackComponentDescriptor", "RNSSearchBarComponentDescriptor", "RNSScreenComponentDescriptor", "RNSScreenFooterComponentDescriptor", "RNSScreenContentWrapperComponentDescriptor", "RNSModalScreenComponentDescriptor"], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-svg": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg", "name": "react-native-svg", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\RNSVG.podspec", "version": "15.12.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\android", "packageImportPath": "import com.horcrux.svg.SvgPackage;", "packageInstance": "new SvgPackage()", "buildTypes": [], "libraryName": "rnsvg", "componentDescriptors": ["RNSVGCircleComponentDescriptor", "RNSVGClipPathComponentDescriptor", "RNSVGDefsComponentDescriptor", "RNSVGFeBlendComponentDescriptor", "RNSVGFeColorMatrixComponentDescriptor", "RNSVGFeCompositeComponentDescriptor", "RNSVGFeFloodComponentDescriptor", "RNSVGFeGaussianBlurComponentDescriptor", "RNSVGFeMergeComponentDescriptor", "RNSVGFeOffsetComponentDescriptor", "RNSVGFilterComponentDescriptor", "RNSVGEllipseComponentDescriptor", "RNSVGForeignObjectComponentDescriptor", "RNSVGGroupComponentDescriptor", "RNSVGImageComponentDescriptor", "RNSVGLinearGradientComponentDescriptor", "RNSVGLineComponentDescriptor", "RNSVGMarkerComponentDescriptor", "RNSVGMaskComponentDescriptor", "RNSVGPathComponentDescriptor", "RNSVGPatternComponentDescriptor", "RNSVGRadialGradientComponentDescriptor", "RNSVGRectComponentDescriptor", "RNSVGSvgViewAndroidComponentDescriptor", "RNSVGSymbolComponentDescriptor", "RNSVGTextComponentDescriptor", "RNSVGTextPathComponentDescriptor", "RNSVGTSpanComponentDescriptor", "RNSVGUseComponentDescriptor"], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-vector-icons": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vector-icons", "name": "react-native-vector-icons", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vector-icons\\RNVectorIcons.podspec", "version": "10.2.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vector-icons\\android", "packageImportPath": "import com.oblador.vectoricons.VectorIconsPackage;", "packageInstance": "new VectorIconsPackage()", "buildTypes": [], "libraryName": "RNVectorIconsSpec", "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-video": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-video", "name": "react-native-video", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-video\\react-native-video.podspec", "version": "6.14.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-video\\android", "packageImportPath": "import com.brentvatne.react.ReactVideoPackage;", "packageInstance": "new ReactVideoPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-video/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-view-shot": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-view-shot", "name": "react-native-view-shot", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-view-shot\\react-native-view-shot.podspec", "version": "4.0.3", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-view-shot\\android", "packageImportPath": "import fr.greweb.reactnativeviewshot.RNViewShotPackage;", "packageInstance": "new RNViewShotPackage()", "buildTypes": [], "libraryName": "rnviewshot", "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-vision-camera": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vision-camera", "name": "react-native-vision-camera", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vision-camera\\VisionCamera.podspec", "version": "4.7.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vision-camera\\android", "packageImportPath": "import com.mrousavy.camera.react.CameraPackage;", "packageInstance": "new CameraPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-vision-camera/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-voip-push-notification": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-voip-push-notification", "name": "react-native-voip-push-notification", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-voip-push-notification\\RNVoipPushNotification.podspec", "version": "3.3.3", "configurations": [], "scriptPhases": []}, "android": null}}, "react-native-webview": {"root": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-webview", "name": "react-native-webview", "platforms": {"ios": {"podspecPath": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-webview\\react-native-webview.podspec", "version": "13.14.2", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-webview\\android", "packageImportPath": "import com.reactnativecommunity.webview.RNCWebViewPackage;", "packageInstance": "new RNCWebViewPackage()", "buildTypes": [], "libraryName": "RNCWebViewSpec", "componentDescriptors": ["RNCWebViewComponentDescriptor"], "cmakeListsPath": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}}, "commands": [{"name": "bundle", "description": "Build the bundle for the provided JavaScript entry file.", "options": [{"name": "--entry-file <path>", "description": "Path to the root JS file, either absolute or relative to JS root"}, {"name": "--platform <string>", "description": "Either \"ios\" or \"android\"", "default": "ios"}, {"name": "--transformer <string>", "description": "Specify a custom transformer to be used"}, {"name": "--dev [boolean]", "description": "If false, warnings are disabled and the bundle is minified", "default": true}, {"name": "--minify [boolean]", "description": "Allows overriding whether bundle is minified. This defaults to false if dev is true, and true if dev is false. Disabling minification can be useful for speeding up production builds for testing purposes."}, {"name": "--bundle-output <string>", "description": "File name where to store the resulting bundle, ex. /tmp/groups.bundle"}, {"name": "--bundle-encoding <string>", "description": "Encoding the bundle should be written in (https://nodejs.org/api/buffer.html#buffer_buffer).", "default": "utf8"}, {"name": "--max-workers <number>", "description": "Specifies the maximum number of workers the worker-pool will spawn for transforming files. This defaults to the number of the cores available on your machine."}, {"name": "--sourcemap-output <string>", "description": "File name where to store the sourcemap file for resulting bundle, ex. /tmp/groups.map"}, {"name": "--sourcemap-sources-root <string>", "description": "Path to make sourcemap's sources entries relative to, ex. /root/dir"}, {"name": "--sourcemap-use-absolute-path", "description": "Report SourceMapURL using its full path", "default": false}, {"name": "--assets-dest <string>", "description": "Directory name where to store assets referenced in the bundle"}, {"name": "--unstable-transform-profile <string>", "description": "Experimental, transform JS for a specific JS engine. Currently supported: hermes, hermes-canary, default", "default": "default"}, {"name": "--asset-catalog-dest [string]", "description": "Path where to create an iOS Asset Catalog for images"}, {"name": "--reset-cache", "description": "Removes cached files", "default": false}, {"name": "--read-global-cache", "description": "Try to fetch transformed JS code from the global cache, if configured.", "default": false}, {"name": "--config <string>", "description": "Path to the CLI configuration file"}, {"name": "--resolver-option <string...>", "description": "Custom resolver options of the form key=value. URL-encoded. May be specified multiple times."}]}, {"name": "start", "description": "Start the React Native development server.", "options": [{"name": "--port <number>"}, {"name": "--host <string>", "default": ""}, {"name": "--projectRoot <path>", "description": "Path to a custom project root"}, {"name": "--watchFolders <list>", "description": "Specify any additional folders to be added to the watch list"}, {"name": "--assetPlugins <list>", "description": "Specify any additional asset plugins to be used by the packager by full filepath"}, {"name": "--sourceExts <list>", "description": "Specify any additional source extensions to be used by the packager"}, {"name": "--max-workers <number>", "description": "Specifies the maximum number of workers the worker-pool will spawn for transforming files. This defaults to the number of the cores available on your machine."}, {"name": "--transformer <string>", "description": "Specify a custom transformer to be used"}, {"name": "--reset-cache, --resetCache", "description": "Removes cached files"}, {"name": "--custom-log-reporter-path, --customLogReporterPath <string>", "description": "Path to a JavaScript file that exports a log reporter as a replacement for TerminalReporter"}, {"name": "--https", "description": "Enables https connections to the server"}, {"name": "--key <path>", "description": "Path to custom SSL key"}, {"name": "--cert <path>", "description": "Path to custom SSL cert"}, {"name": "--config <string>", "description": "Path to the CLI configuration file"}, {"name": "--no-interactive", "description": "Disables interactive mode"}, {"name": "--client-logs", "description": "[Deprecated] Enable plain text JavaScript log streaming for all connected apps. This feature is deprecated and will be removed in future.", "default": false}]}, {"name": "codegen", "options": [{"name": "--path <path>", "description": "Path to the React Native project root.", "default": "F:\\A1\\adtip-reactnative\\Adtip"}, {"name": "--platform <string>", "description": "Target platform. Supported values: \"android\", \"ios\", \"all\".", "default": "all"}, {"name": "--outputPath <path>", "description": "Path where generated artifacts will be output to."}, {"name": "--source <string>", "description": "Whether the script is invoked from an `app` or a `library`", "default": "app"}]}, {"name": "log-ios", "description": "starts iOS device syslog tail", "options": [{"name": "-i --interactive", "description": "Explicitly select simulator to tail logs from. By default it will tail logs from the first booted and available simulator."}]}, {"name": "run-ios", "description": "builds your app and starts it on iOS simulator", "examples": [{"desc": "Run on a different simulator, e.g. iPhone SE (2nd generation)", "cmd": "npx react-native run-ios --simulator \"iPhone SE (2nd generation)\""}, {"desc": "Run on a connected device, e.g. <PERSON>'s iPhone", "cmd": "npx react-native run-ios --device \"Max's iPhone\""}, {"desc": "Run on the AppleTV simulator", "cmd": "npx react-native run-ios --simulator \"Apple TV\"  --scheme \"helloworld-tvOS\""}], "options": [{"name": "--no-packager", "description": "Do not launch packager while running the app"}, {"name": "--port <number>", "default": 8081}, {"name": "--terminal <string>", "description": "Launches the Metro Bundler in a new window using the specified terminal path.", "default": "cmd.exe"}, {"name": "--binary-path <string>", "description": "Path relative to project root where pre-built .app binary lives."}, {"name": "--list-devices", "description": "List all available iOS devices and simulators and let you choose one to run the app. "}, {"name": "--udid <string>", "description": "Explicitly set the device to use by UDID"}, {"name": "--simulator <string>", "description": "Explicitly set the simulator to use. Optionally set the iOS version between parentheses at the end to match an exact version: \"iPhone 15 (17.0)\""}, {"name": "--mode <string>", "description": "Explicitly set the scheme configuration to use. This option is case sensitive."}, {"name": "--scheme <string>", "description": "Explicitly set Xcode scheme to use"}, {"name": "--destination <string>", "description": "Explicitly extend destination e.g. \"arch=x86_64\""}, {"name": "--verbose", "description": "Do not use xcbeautify or xcpretty even if installed"}, {"name": "--xcconfig [string]", "description": "Explicitly set xcconfig to use"}, {"name": "--buildFolder <string>", "description": "Location for iOS build artifacts. Corresponds to Xcode's \"-derivedDataPath\"."}, {"name": "--extra-params <string>", "description": "Custom params that will be passed to xcodebuild command."}, {"name": "--target <string>", "description": "Explicitly set Xcode target to use."}, {"name": "-i --interactive", "description": "Explicitly select which scheme and configuration to use before running a build"}, {"name": "--force-pods", "description": "Force CocoaPods installation"}, {"name": "--device [string]", "description": "Explicitly set the device to use by name or by unique device identifier . If the value is not provided,the app will run on the first available physical device."}]}, {"name": "build-ios", "description": "builds your app for iOS platform", "examples": [{"desc": "Build the app for all iOS devices in Release mode", "cmd": "npx react-native build-ios --mode \"Release\""}], "options": [{"name": "--mode <string>", "description": "Explicitly set the scheme configuration to use. This option is case sensitive."}, {"name": "--scheme <string>", "description": "Explicitly set Xcode scheme to use"}, {"name": "--destination <string>", "description": "Explicitly extend destination e.g. \"arch=x86_64\""}, {"name": "--verbose", "description": "Do not use xcbeautify or xcpretty even if installed"}, {"name": "--xcconfig [string]", "description": "Explicitly set xcconfig to use"}, {"name": "--buildFolder <string>", "description": "Location for iOS build artifacts. Corresponds to Xcode's \"-derivedDataPath\"."}, {"name": "--extra-params <string>", "description": "Custom params that will be passed to xcodebuild command."}, {"name": "--target <string>", "description": "Explicitly set Xcode target to use."}, {"name": "-i --interactive", "description": "Explicitly select which scheme and configuration to use before running a build"}, {"name": "--force-pods", "description": "Force CocoaPods installation"}, {"name": "--device [string]", "description": "Explicitly set the device to use by name or by unique device identifier . If the value is not provided,the app will run on the first available physical device."}]}, {"name": "log-android", "description": "starts logki<PERSON>"}, {"name": "run-android", "description": "builds your app and starts it on a connected Android emulator or device", "options": [{"name": "--mode <string>", "description": "Specify your app's build variant"}, {"name": "--tasks <list>", "description": "Run custom Gradle tasks. By default it's \"assembleDebug\". Will override passed mode and variant arguments."}, {"name": "--active-arch-only", "description": "Build native libraries only for the current device architecture for debug builds.", "default": false}, {"name": "--extra-params <string>", "description": "Custom params passed to gradle build command"}, {"name": "-i --interactive", "description": "Explicitly select build type and flavour to use before running a build"}, {"name": "--no-packager", "description": "Do not launch packager while running the app"}, {"name": "--port <number>", "default": 8081}, {"name": "--terminal <string>", "description": "Launches the Metro Bundler in a new window using the specified terminal path.", "default": "cmd.exe"}, {"name": "--appId <string>", "description": "Specify an applicationId to launch after build. If not specified, `package` from AndroidManifest.xml will be used.", "default": ""}, {"name": "--appIdSuffix <string>", "description": "Specify an applicationIdSuffix to launch after build.", "default": ""}, {"name": "--main-activity <string>", "description": "Name of the activity to start"}, {"name": "--device <string>", "description": "Explicitly set the device to use by name. The value is not required if you have a single device connected."}, {"name": "--deviceId <string>", "description": "**DEPRECATED** Builds your app and starts it on a specific device/simulator with the given device id (listed by running \"adb devices\" on the command line)."}, {"name": "--list-devices", "description": "Lists all available Android devices and simulators and let you choose one to run the app", "default": false}, {"name": "--binary-path <string>", "description": "Path relative to project root where pre-built .apk binary lives."}, {"name": "--user <number>", "description": "Id of the User Profile you want to install the app on."}]}, {"name": "build-android", "description": "builds your app", "options": [{"name": "--mode <string>", "description": "Specify your app's build variant"}, {"name": "--tasks <list>", "description": "Run custom Gradle tasks. By default it's \"assembleDebug\". Will override passed mode and variant arguments."}, {"name": "--active-arch-only", "description": "Build native libraries only for the current device architecture for debug builds.", "default": false}, {"name": "--extra-params <string>", "description": "Custom params passed to gradle build command"}, {"name": "-i --interactive", "description": "Explicitly select build type and flavour to use before running a build"}]}], "healthChecks": [], "platforms": {"ios": {}, "android": {}}, "assets": [], "project": {"ios": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\ios", "xcodeProject": {"name": "Adtip.xcodeproj", "path": ".", "isWorkspace": false}, "assets": []}, "android": {"sourceDir": "F:\\A1\\adtip-reactnative\\Adtip\\android", "appName": "app", "packageName": "com.adtip.app.adtip_app", "applicationId": "com.adtip.app.adtip_app", "mainActivity": ".MainActivity", "assets": []}}}